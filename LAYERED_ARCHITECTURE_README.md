# Campaign Reporting - Layered Architecture

This document explains the refactored layered architecture for the campaign reporting system.

## Architecture Overview

The original `campaign_whatsapp_report.py` has been refactored into a clean 3-layer architecture:

### 1. **Data Layer** (`campaign_reporting/data/`)
Handles all external data access and file operations.

- `campaign_data_service.py` - MercadoLibre API operations
- `merchant_data_service.py` - GoBots API operations  
- `file_service.py` - File I/O operations

### 2. **Business Layer** (`campaign_reporting/business/`)
Contains core business logic and calculations.

- `report_generator.py` - WhatsApp report generation logic
- `campaign_processor.py` - Campaign processing orchestration

### 3. **Presentation Layer** (`campaign_reporting/presentation/`)
Handles user interface and main application flow.

- `main_controller.py` - Main execution orchestration

### 4. **Configuration** (`campaign_reporting/config/`)
Centralized configuration management.

- `settings.py` - All configuration constants and settings

## Folder Structure

```
campaign_reporting/
├── __init__.py
├── config/
│   ├── __init__.py
│   └── settings.py              # Configuration constants
├── data/
│   ├── __init__.py
│   ├── campaign_data_service.py # MercadoLibre API calls
│   ├── merchant_data_service.py # GoBots API calls
│   └── file_service.py          # File operations
├── business/
│   ├── __init__.py
│   ├── report_generator.py      # Report generation logic
│   └── campaign_processor.py    # Campaign processing
└── presentation/
    ├── __init__.py
    └── main_controller.py       # Main orchestration
```

## Key Benefits

### 1. **Separation of Concerns**
- Each layer has a single, well-defined responsibility
- Data access is separated from business logic
- Business logic is separated from presentation

### 2. **Improved Testability**
- Each function can be tested independently
- Easy to mock dependencies for unit testing
- Clear interfaces between layers

### 3. **Better Maintainability**
- Changes in one layer don't affect others
- Easy to locate and modify specific functionality
- Reduced code duplication

### 4. **Enhanced Readability**
- Smaller, focused files
- Clear function names and documentation
- Logical organization of related functionality

## Migration Guide

### Running the New System

Instead of running the original file:
```bash
python campaign_whatsapp_report.py
```

Use the new entry point:
```bash
python campaign_reporting_main.py
```

### Key Changes

1. **Configuration Centralized**: All constants moved to `config/settings.py`
2. **Data Access Separated**: API calls isolated in data layer
3. **Business Logic Extracted**: Report generation logic in business layer
4. **Main Flow Simplified**: Clean orchestration in presentation layer

## Function Mapping

### Original → New Location

| Original Function | New Location |
|------------------|--------------|
| `create_retry_options()` | `config/settings.py` |
| `generate_whatsapp_performance_report()` | `business/report_generator.py` |
| `gerar_relatorio_completo()` | `business/campaign_processor.py` |
| `main()` | `presentation/main_controller.py` |

## Testing Strategy

Each layer can be tested independently:

```python
# Test data layer
from campaign_reporting.data import campaign_data_service
# Mock external APIs and test data access

# Test business layer  
from campaign_reporting.business import report_generator
# Test report generation with sample data

# Test presentation layer
from campaign_reporting.presentation import main_controller
# Test orchestration logic
```

## Future Enhancements

The layered architecture makes it easy to add:

1. **New Report Formats**: Add to business layer
2. **Different Data Sources**: Add to data layer
3. **New Output Methods**: Add to presentation layer
4. **Caching Strategies**: Modify data layer
5. **Error Handling**: Add to appropriate layer

## Dependencies

The refactored system maintains all original dependencies:
- `aiohttp` for HTTP requests
- `pandas` for data processing
- `aiohttp_client_cache` for caching
- `aiohttp_retry` for retry logic
- All existing service modules

## Backward Compatibility

The original `campaign_whatsapp_report.py` file remains unchanged and functional. The new architecture is a complete reimplementation that can run alongside the original system.
