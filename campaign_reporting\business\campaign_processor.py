"""
Campaign Processor

Handles campaign data processing and business logic.
"""
import logging
from items_report import generate_complete_perfromance_report
from campaign_reporting.data.campaign_data_service import (
    fetch_advertiser_campaigns, 
    fetch_campaign_details, 
    fetch_campaign_products
)
from campaign_reporting.data.file_service import write_whatsapp_report, append_campaign_to_csv
from campaign_reporting.business.report_generator import generate_whatsapp_performance_report

logger = logging.getLogger(__name__)


async def process_merchant_campaigns(session, merchant_info):
    """
    Process all campaigns for a single merchant.
    
    Args:
        session: aiohttp session
        merchant_info: Dictionary containing merchant and access_token
        
    Returns:
        List of processed campaigns
    """
    merchant = merchant_info['merchant']
    access_token = merchant_info['access_token']

    # Extract user_id from access_token for MercadoLibreClient
    user_id = access_token.split('-')[-1] if '-' in access_token else '1'
    user_id = int(user_id)

    logger.info(f"Processing campaigns for merchant {merchant}")

    # Fetch campaigns
    campaigns = await fetch_advertiser_campaigns(access_token, user_id)
    if not campaigns:
        return []

    campaigns_list = []

    for campaign in campaigns:
        try:
            # Process individual campaign
            campaign_result = await process_single_campaign(
                session, 
                campaign, 
                access_token, 
                user_id, 
                merchant
            )
            
            if campaign_result:
                campaigns_list.append(campaign_result)
                
        except Exception as e:
            logger.error(f"Error processing campaign {campaign.get('id', 'unknown')}: {e}")
            continue

    logger.info(f"Processed {len(campaigns_list)} campaigns for merchant {merchant}")
    return campaigns_list


async def process_single_campaign(session, campaign, access_token, user_id, merchant):
    """
    Process a single campaign.
    
    Args:
        session: aiohttp session
        campaign: Campaign data
        access_token: MercadoLibre API access token
        user_id: User ID
        merchant: Merchant identifier
        
    Returns:
        Processed campaign data or None
    """
    campaign_id = campaign['id']
    date_created = campaign['date_created']
    
    logger.info(f"Processing campaign {campaign_id} for merchant {merchant}")

    # Fetch campaign details
    campaign_data = await fetch_campaign_details(access_token, user_id, campaign_id, date_created)
    if not campaign_data:
        return None

    # Only process campaigns with clicks
    if not campaign_data.get('metrics', {}).get("clicks"):
        logger.info(f"Campaign {campaign_id} has no clicks, skipping")
        return None

    try:
        # Generate WhatsApp report
        whatsapp_report = generate_whatsapp_performance_report(campaign_data, None, 'pt')
        write_whatsapp_report(whatsapp_report, merchant, campaign_id)

        # Fetch campaign products for complete report
        advertiser_id = await _get_advertiser_id(access_token, user_id)
        if advertiser_id:
            campaign_products = await fetch_campaign_products(
                access_token, 
                user_id, 
                advertiser_id, 
                campaign_id
            )
            
            # Generate complete performance report
            await generate_complete_perfromance_report(
                campaign_data, 
                campaign_products, 
                campaign_id, 
                merchant, 
                access_token, 
                'pt_BR'
            )

        # Append to CSV
        append_campaign_to_csv(campaign_data, merchant)
        
        logger.info(f"Successfully processed campaign {campaign_id}")
        return campaign_data

    except Exception as e:
        logger.error(f"Error processing campaign {campaign_id}: {e}")
        return None


async def _get_advertiser_id(access_token, user_id):
    """
    Get advertiser ID for a user.
    
    Args:
        access_token: MercadoLibre API access token
        user_id: User ID
        
    Returns:
        Advertiser ID or None
    """
    from services.mercadolibre_service import MercadoLibreClient
    
    try:
        async with MercadoLibreClient(access_token, user_id) as client:
            return await client.get_advertiser_id_pads()
    except Exception as e:
        logger.error(f"Error getting advertiser ID: {e}")
        return None


def should_process_campaign(campaign_data):
    """
    Determine if a campaign should be processed.
    
    Args:
        campaign_data: Campaign data dictionary
        
    Returns:
        Boolean indicating if campaign should be processed
    """
    # Check if campaign has metrics and clicks
    metrics = campaign_data.get('metrics', {})
    return bool(metrics.get('clicks', 0) > 0)


def validate_campaign_data(campaign_data):
    """
    Validate campaign data structure.
    
    Args:
        campaign_data: Campaign data dictionary
        
    Returns:
        Boolean indicating if data is valid
    """
    required_fields = ['id', 'name', 'metrics', 'acos_target', 'budget']
    
    for field in required_fields:
        if field not in campaign_data:
            logger.warning(f"Missing required field: {field}")
            return False
    
    # Check metrics structure
    metrics = campaign_data.get('metrics', {})
    required_metrics = ['cost', 'total_amount', 'acos']
    
    for metric in required_metrics:
        if metric not in metrics:
            logger.warning(f"Missing required metric: {metric}")
            return False
    
    return True
