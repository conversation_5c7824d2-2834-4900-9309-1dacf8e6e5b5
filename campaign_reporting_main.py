"""
Campaign Reporting - Layered Architecture Entry Point

This is the new main entry point for the refactored campaign reporting system.
"""
import asyncio
import sys
import os

# Add the current directory to the path so we can import modules
sys.path.append(os.path.dirname(__file__))

import logging_config
from campaign_reporting.presentation.main_controller import run_campaign_reporting

# Setup logging
logger = logging_config.setup_logging(__name__)


async def main():
    """Main entry point for the campaign reporting application."""
    try:
        logger.info("Starting Campaign Reporting Application")
        await run_campaign_reporting()
        logger.info("Campaign Reporting Application completed successfully")
    except Exception as e:
        logger.error(f"Campaign Reporting Application failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
