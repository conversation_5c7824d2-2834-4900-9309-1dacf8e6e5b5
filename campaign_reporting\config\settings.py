"""
Configuration settings for campaign reporting.
"""
from datetime import datetime, timedelta
from aiohttp_retry import ExponentialRetry
import aiohttp

# Date configuration
DATE_FORMAT = "%Y-%m-%d"
DATE_TO = datetime.now().strftime(DATE_FORMAT)
DATE_FROM = (datetime.now() - timedelta(days=30)).strftime(DATE_FORMAT)
DATE_FROM_TWO_MONTHS = (datetime.now() - timedelta(days=60)).strftime(DATE_FORMAT)

# Cache configuration
CACHE_EXPIRATION_HOURS = 3
CACHE_EXPIRATION_SECONDS = 3600 * CACHE_EXPIRATION_HOURS

# Retry configuration
DEFAULT_MAX_RETRIES = 5
DEFAULT_INITIAL_BACKOFF = 1
DEFAULT_MAX_BACKOFF = 60

# File paths
OUTPUT_CAMPAIGNS_DIR = 'output_campaigns'
OUTPUT_CSV_FILE = 'output_campaigns/output_campaigns.csv'
MERCHANTS_FILE = 'merchants.txt'
SELLER_IDS_FILE = 'seller_ids.txt'

def create_retry_options(max_retries=DEFAULT_MAX_RETRIES, initial_backoff=DEFAULT_INITIAL_BACKOFF, max_backoff=DEFAULT_MAX_BACKOFF):
    """Create retry options for aiohttp requests."""
    return ExponentialRetry(
        attempts=max_retries,
        start_timeout=initial_backoff,
        max_timeout=max_backoff,
        factor=2.0,  # Exponential backoff factor
        statuses={429},  # Only retry on rate limit errors
        exceptions={aiohttp.ClientResponseError},
    )
