"""
File Service

Handles all file I/O operations.
"""
import os
import pandas as pd
import logging
from campaign_reporting.config.settings import OUTPUT_CAMPAIGNS_DIR, OUTPUT_CSV_FILE

logger = logging.getLogger(__name__)


def ensure_output_directory():
    """Ensure the output directory exists."""
    os.makedirs(OUTPUT_CAMPAIGNS_DIR, exist_ok=True)


def clean_output_file():
    """Remove existing output CSV file to start fresh."""
    if os.path.exists(OUTPUT_CSV_FILE):
        os.remove(OUTPUT_CSV_FILE)


def write_whatsapp_report(content: str, merchant: str, campaign_id: str):
    """
    Write WhatsApp report to file.
    
    Args:
        content: Report content
        merchant: Merchant identifier
        campaign_id: Campaign ID
    """
    report_file_path = f'{OUTPUT_CAMPAIGNS_DIR}/{merchant}__{campaign_id}.txt'
    try:
        with open(report_file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        logger.info(f"WhatsApp report written to {report_file_path}")
    except Exception as e:
        logger.error(f"Error writing WhatsApp report: {e}")


def append_campaign_to_csv(campaign_data, merchant: str):
    """
    Append campaign data to CSV file.
    
    Args:
        campaign_data: Campaign data to append
        merchant: Merchant identifier
    """
    try:
        # Normalize and append to main CSV file
        df = pd.json_normalize(campaign_data, sep='_')
        if not df.empty:
            # Add user ID column to identify which user this data belongs to
            df['user_id'] = merchant
            df['merchant_id'] = merchant

            # Check if file exists to determine if we need to write headers
            file_exists = os.path.isfile(OUTPUT_CSV_FILE)

            # Append to the CSV file with or without headers
            df.to_csv(OUTPUT_CSV_FILE, mode='a', header=not file_exists, index=False)
            logger.info(f"Campaign data appended to CSV for merchant {merchant}")
        else:
            logger.warning(f"Empty dataframe for merchant {merchant}")
    except Exception as e:
        logger.error(f"Error appending to CSV: {e}")


def setup_cache_directory():
    """
    Set up cache directory for API requests.
    
    Returns:
        Path to cache directory
    """
    cache_dir = os.path.join(os.getcwd(), 'cache')
    os.makedirs(cache_dir, exist_ok=True)
    logger.info(f"Cache directory set up at: {cache_dir}")
    return cache_dir
