"""
Main Controller

Orchestrates the entire campaign reporting process.
"""
import asyncio
import logging
import sys
import os

# Add the project root to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from aiohttp_client_cache.session import CachedSession
from aiohttp_client_cache.backends.filesystem import FileBackend
from aiohttp_retry import RetryClient

from campaign_reporting.config.settings import (
    CACHE_EXPIRATION_SECONDS,
    create_retry_options,
    OUTPUT_CSV_FILE
)
from campaign_reporting.data.file_service import (
    ensure_output_directory,
    clean_output_file,
    setup_cache_directory
)
from campaign_reporting.data.merchant_data_service import (
    fetch_gobots_data,
    load_merchant_ids,
    load_seller_ids,
    filter_merchants,
    group_merchants_by_id
)
from campaign_reporting.business.campaign_processor import process_merchant_campaigns

logger = logging.getLogger(__name__)


async def run_campaign_reporting():
    """
    Main entry point for campaign reporting process.
    """
    logger.info("Starting campaign reporting process")

    # Setup
    _setup_environment()

    # Create session with caching and retry logic
    async with _create_session() as session:
        # Load merchant data
        merchants_data = await _load_merchants_data(session)
        if not merchants_data:
            return

        # Process campaigns for each merchant
        await _process_all_merchants(session, merchants_data)

    logger.info(f"Campaign reporting completed. Results saved to {OUTPUT_CSV_FILE}")


def _setup_environment():
    """Setup the environment for campaign reporting."""
    logger.info("Setting up environment")
    ensure_output_directory()
    clean_output_file()


def _create_session():
    """
    Create aiohttp session with caching and retry capabilities.

    Returns:
        Configured aiohttp session
    """
    # Set up cache
    cache_dir = setup_cache_directory()

    logger.info(f"Setting up cache with {CACHE_EXPIRATION_SECONDS/3600}-hour expiration")

    # Create a file cache
    cache = FileBackend(
        cache_name=cache_dir,
        expire_after=CACHE_EXPIRATION_SECONDS,
        allowed_methods=('GET', 'POST'),  # Cache both GET and POST requests
        include_headers=True,             # Include headers in the cache key
        ignored_params=['access_token'],  # Don't include access tokens in cache keys for security
        cache_control=True                # Respect Cache-Control headers from the server
    )

    # Create retry options
    retry_options = create_retry_options(
        max_retries=5,
        initial_backoff=2,
        max_backoff=120
    )

    # Create a cached session
    cached_session = CachedSession(cache=cache)

    # Wrap the cached session with RetryClient for automatic retries
    return RetryClient(
        client_session=cached_session,
        retry_options=retry_options
    )


async def _load_merchants_data(session):
    """
    Load and filter merchants data.

    Args:
        session: aiohttp session

    Returns:
        Filtered merchants data or None if error
    """
    logger.info("Loading merchants data")

    # Load merchant and seller IDs from files
    merchant_ids = load_merchant_ids()
    seller_ids = load_seller_ids()

    if not merchant_ids:
        logger.error("No merchant IDs loaded")
        return None

    # Fetch GoBots data
    go_bots_data = await fetch_gobots_data(session)
    if not go_bots_data:
        return None

    # Filter merchants
    filtered_merchants = filter_merchants(go_bots_data, merchant_ids, seller_ids)

    if not filtered_merchants:
        logger.error("No merchants found after filtering")
        return None

    logger.info(f"Loaded {len(filtered_merchants)} merchants")
    return filtered_merchants


async def _process_all_merchants(session, merchants_data):
    """
    Process campaigns for all merchants.

    Args:
        session: aiohttp session
        merchants_data: List of merchant data
    """
    # Group merchants by ID
    merchants_by_id = group_merchants_by_id(merchants_data)

    logger.info(f"Processing {len(merchants_by_id)} unique merchants")

    # Process each merchant
    for merchant_id, merchants_matches in merchants_by_id.items():
        logger.info(f"Processing merchant {merchant_id}")

        try:
            # Process only the first match for each merchant
            # (taking only [:1] as in original code)
            tasks = [
                process_merchant_campaigns(session, merchant)
                for merchant in merchants_matches[:1]
            ]

            await asyncio.gather(*tasks)

            logger.info(f"Completed processing merchant {merchant_id}")

        except Exception as e:
            logger.error(f"Error processing merchant {merchant_id}: {e}")
            continue


def format_final_summary(total_merchants, total_campaigns):
    """
    Format final summary message.

    Args:
        total_merchants: Number of merchants processed
        total_campaigns: Number of campaigns processed

    Returns:
        Formatted summary string
    """
    return f"""
Campaign Reporting Summary:
- Merchants processed: {total_merchants}
- Campaigns processed: {total_campaigns}
- Output file: {OUTPUT_CSV_FILE}
"""
