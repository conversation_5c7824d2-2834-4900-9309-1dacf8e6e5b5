"""
Merchant Data Service

Handles all merchant-related data access operations.
"""
import logging
from services import gobots_service
from campaign_reporting.config.settings import MERCHANTS_FILE, SELLER_IDS_FILE

logger = logging.getLogger(__name__)


async def fetch_gobots_data(session):
    """
    Fetch data from GoBots API.
    
    Args:
        session: aiohttp session
        
    Returns:
        GoBots data or None if error
    """
    go_bots_data = await gobots_service.get_api_response(session)
    if not go_bots_data:
        logger.error("Failed to fetch GoBots data")
        return None

    # Ensure go_bots_data is a list
    if not isinstance(go_bots_data, list):
        logger.error("GoBots data is not in expected list format")
        return None
        
    return go_bots_data


def load_merchant_ids():
    """
    Load merchant IDs from file.
    
    Returns:
        List of merchant IDs
    """
    try:
        with open(MERCHANTS_FILE, 'r') as f:
            merchants_ids = [uid.strip() for uid in f.read().split(',')]
        return merchants_ids
    except FileNotFoundError:
        logger.error(f"Merchants file {MERCHANTS_FILE} not found")
        return []
    except Exception as e:
        logger.error(f"Error loading merchant IDs: {e}")
        return []


def load_seller_ids():
    """
    Load seller IDs from file.
    
    Returns:
        List of seller IDs
    """
    try:
        with open(SELLER_IDS_FILE, 'r') as f:
            seller_ids = [r.strip() for r in f.read().split(',')]
            # Check if not empty
            if seller_ids[0] == '':
                seller_ids = []
        return seller_ids
    except FileNotFoundError:
        logger.error(f"Seller IDs file {SELLER_IDS_FILE} not found")
        return []
    except Exception as e:
        logger.error(f"Error loading seller IDs: {e}")
        return []


def filter_merchants(go_bots_data, merchant_ids, seller_ids):
    """
    Filter merchants data based on provided criteria.
    
    Args:
        go_bots_data: Raw GoBots data
        merchant_ids: List of merchant IDs to filter by
        seller_ids: List of seller IDs to filter by
        
    Returns:
        Filtered merchant data
    """
    filters = {
        'merchant_ids': merchant_ids,
        'seller_ids': seller_ids if seller_ids else None
    }
    
    return gobots_service.filter_merchants_data(go_bots_data, filters)


def group_merchants_by_id(merchants_matches):
    """
    Group merchants by merchant ID.
    
    Args:
        merchants_matches: List of merchant matches
        
    Returns:
        Dictionary grouped by merchant ID
    """
    merchants_by_id = {}
    for match in merchants_matches:
        merchant_id = match['merchant']
        if merchant_id not in merchants_by_id:
            merchants_by_id[merchant_id] = []
        merchants_by_id[merchant_id].append(match)
    
    return merchants_by_id
