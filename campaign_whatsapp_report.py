import os
import aiohttp
import asyncio
from datetime import datetime, timedelta
from aiohttp_client_cache.session import CachedSession
from aiohttp_client_cache.backends.filesystem import FileBackend
from aiohttp_retry import RetryClient, ExponentialRetry

import pandas as pd

from items_report import generate_complete_perfromance_report
from translations import get_translation
from services import gobots_service
from services.mercadolibre_service import MercadoLibreClient

import logging_config

logger = logging_config.setup_logging(__name__)



def create_retry_options(max_retries=5, initial_backoff=1, max_backoff=60):
    return ExponentialRetry(
        attempts=max_retries,
        start_timeout=initial_backoff,
        max_timeout=max_backoff,
        factor=2.0,  # Exponential backoff factor
        statuses={429},  # Only retry on rate limit errors
        exceptions={aiohttp.ClientResponseError},
    )

DATE_FORMAT = "%Y-%m-%d"
DATE_TO = datetime.now().strftime(DATE_FORMAT)
DATE_FROM = (datetime.now() - timedelta(days=30)).strftime(DATE_FORMAT)
DATE_FROM_TWO_MONTHS  = (datetime.now() - timedelta(days=60)).strftime(DATE_FORMAT)


def generate_whatsapp_performance_report(campaign_data, campaign_products=None, language="pt"):
    days_running = (datetime.fromisoformat(campaign_data['last_updated']) -
                    datetime.fromisoformat(campaign_data['date_created'])).days + 1
    average_daily_cost = campaign_data['metrics']['cost'] / days_running if days_running > 0 else 0

    # Note: campaign_products parameter is kept for backward compatibility
    # but is not currently used in this function

    def format_currency(value, locale="pt_BR"):
        # Get the currency symbol from the translation system
        currency_symbol = get_translation('currency_symbol', locale)
        return f"{currency_symbol} {value:,.2f}".replace(',', 'vtemp').replace('.', ',').replace('vtemp', '.')

    # Calculate values needed for translations
    ads_sales = campaign_data['metrics']['total_amount']
    organic_sales = campaign_data['metrics']['organic_units_amount']
    total_sales = ads_sales + organic_sales

    # Calculate ROAS
    roas = campaign_data['metrics']['total_amount'] / campaign_data['metrics']['cost'] if campaign_data['metrics']['cost'] > 0 else 0

    # Initialize variables that might be used in translations
    deviation = 0
    total = 0
    ad_sales_percentage = 0
    organic_sales_percentage = 0
    lost_rank_share = 0
    lost_budget_share = 0
    target_acos = campaign_data['acos_target']
    acos_benchmark = campaign_data['metrics'].get('acos_benchmark', 0)
    acos_difference = 0

    if campaign_data['metrics'].get('acos_benchmark') and campaign_data['acos_target']:
        acos_difference = abs(target_acos - acos_benchmark) / acos_benchmark * 100 if acos_benchmark else 0

    if campaign_data['metrics']['advertising_items_quantity'] > 0:
        total = campaign_data['metrics']['advertising_items_quantity'] + campaign_data['metrics']['organic_items_quantity']
        ad_sales_percentage = (campaign_data['metrics']['advertising_items_quantity'] / total) * 100 if total > 0 else 0
        organic_sales_percentage = (campaign_data['metrics']['organic_items_quantity'] / total) * 100 if total > 0 else 0

    if campaign_data['metrics'].get('lost_impression_share_by_ad_rank'):
        lost_rank_share = campaign_data['metrics']['lost_impression_share_by_ad_rank'] * 100

    if campaign_data['metrics'].get('lost_impression_share_by_budget'):
        lost_budget_share = campaign_data['metrics']['lost_impression_share_by_budget'] * 100

    if campaign_data['metrics']['acos'] > campaign_data['acos_target']:
        deviation = ((campaign_data['metrics']['acos'] / campaign_data['acos_target']) - 1) * 100

    report = []

    # Map language to locale
    locale = "pt_BR" if language == "pt" else "es_AR" if language == "es" else "pt_BR"

    # Format values for translation parameters
    formatted_cost = format_currency(campaign_data['metrics']['cost'], locale)
    formatted_avg_daily_cost = format_currency(average_daily_cost, locale)
    formatted_budget = format_currency(campaign_data['budget'], locale)
    formatted_total_sales = format_currency(total_sales, locale)
    formatted_ads_sales = format_currency(ads_sales, locale)
    formatted_organic_sales = format_currency(organic_sales, locale)

    # Add report sections using the centralized translation system
    report.append(f"*{get_translation('metrics_title', locale, campaign_name=campaign_data['name'])}*")
    report.append(f"*{get_translation('acos_whatsapp', locale, acos_real=campaign_data['metrics']['acos'], acos_target=campaign_data['acos_target'])}*")
    report.append(f"*{get_translation('budget_spent', locale, cost=formatted_cost)}*")
    report.append(f"*{get_translation('daily_cost', locale, avg_daily_cost=formatted_avg_daily_cost, budget=formatted_budget)}*")
    report.append(f"*{get_translation('total_sales_whatsapp', locale, total_sales=formatted_total_sales, ads_sales=formatted_ads_sales, organic_sales=formatted_organic_sales)}*")
    report.append(f"\n*{get_translation('main_indicators', locale)}*")

    if campaign_data['metrics'].get('acos_benchmark'):
        # We already calculated deviation earlier, so we don't need to recalculate it here
        deviation = ((campaign_data['metrics']['acos'] / campaign_data['acos_target']) - 1) * 100 if campaign_data['metrics']['acos'] > campaign_data['acos_target'] else 0

        if campaign_data['metrics']['acos'] > campaign_data['acos_target']:
            if campaign_data['metrics']['acos'] < campaign_data['metrics']['acos_benchmark']:
                report.append(f"*{get_translation('acos_higher_below', locale, deviation=deviation, acos_benchmark=campaign_data['metrics']['acos_benchmark'])}*")
            else:
                report.append(f"*{get_translation('acos_higher_above', locale, deviation=deviation, acos_benchmark=campaign_data['metrics']['acos_benchmark'])}*")
        elif campaign_data['metrics']['acos'] < campaign_data['acos_target']:
            if campaign_data['metrics']['acos'] < campaign_data['metrics']['acos_benchmark']:
                report.append(f"*{get_translation('acos_below_benchmark', locale, acos_benchmark=campaign_data['metrics']['acos_benchmark'])}*")
            else:
                report.append(f"*{get_translation('acos_below_near', locale, acos_benchmark=campaign_data['metrics']['acos_benchmark'])}*")
        else:
            report.append(f"*{get_translation('acos_expected', locale)}*")

    if round(average_daily_cost) > campaign_data['budget']:
        report.append(f"*{get_translation('budget_above', locale)}*")
    else:
        report.append(f"*{get_translation('budget_expected', locale)}*")

    # We already calculated ROAS earlier, so we don't need to recalculate it here
    if roas > 5:
        report.append(f"*{get_translation('roas_impressive', locale, roas=roas)}*")
    elif 2 <= roas <= 5:
        report.append(f"*{get_translation('roas_good', locale, roas=roas)}*")
    else:
        report.append(f"*{get_translation('roas_low', locale, roas=roas)}*")

    report.append(f"\n*{get_translation('ads_performance', locale)}*")
    if campaign_data['metrics']['advertising_items_quantity'] > 0:
        # Calculate these values for translation parameters
        total = campaign_data['metrics']['advertising_items_quantity'] + campaign_data['metrics']['organic_items_quantity']
        ad_sales_percentage = (campaign_data['metrics']['advertising_items_quantity'] / total) * 100 if total > 0 else 0
        organic_sales_percentage = (campaign_data['metrics']['organic_items_quantity'] / total) * 100 if total > 0 else 0

        report.append(f"*{get_translation('positive_effect', locale, total=total)}*")
        report.append(f"   - *{get_translation('via_ads', locale, ad_quantity=campaign_data['metrics']['advertising_items_quantity'], ad_percentage=ad_sales_percentage)}*")
        report.append(f"   - *{get_translation('organic', locale, organic_quantity=campaign_data['metrics']['organic_items_quantity'], organic_percentage=organic_sales_percentage)}*")
        report.append(f"- {get_translation('cvr', locale, cvr=campaign_data['metrics']['cvr'])}")
    else:
        report.append(f"*{get_translation('no_results', locale)}*")

    report.append(f"\n*{get_translation('suggestions', locale)}*")
    has_suggestions = False

    if campaign_data['metrics'].get('lost_impression_share_by_ad_rank', 0) > 0.3:
        has_suggestions = True
        # Calculate lost_rank_share for translation parameter
        lost_rank_share = campaign_data['metrics']['lost_impression_share_by_ad_rank'] * 100
        report.append(f"*{get_translation('improve_rank', locale, lost_rank=lost_rank_share)}*")

    if campaign_data['metrics'].get('lost_impression_share_by_budget', 0) > 0.2:
        has_suggestions = True
        # Calculate lost_budget_share for translation parameter
        lost_budget_share = campaign_data['metrics']['lost_impression_share_by_budget'] * 100
        report.append(f"*{get_translation('increase_budget', locale, lost_budget=lost_budget_share)}*")

    if campaign_data['metrics'].get('acos') and campaign_data['metrics'].get('acos_benchmark'):
        # We already calculated these values earlier
        if acos_difference > 15:
            has_suggestions = True
            if target_acos > acos_benchmark:
                report.append(f"*{get_translation('adjust_acos_above', locale, target_acos=target_acos, acos_difference=acos_difference, acos_benchmark=acos_benchmark)}*")
            else:
                report.append(f"*{get_translation('adjust_acos_below', locale, target_acos=target_acos, acos_difference=acos_difference, acos_benchmark=acos_benchmark)}*")

    if not has_suggestions:
        report.append(f"*{get_translation('no_suggestions', locale)}*")

    return '\n'.join(report)


async def gerar_relatorio_completo(session, merchant_info):
    merchant = merchant_info['merchant']
    access_token = merchant_info['access_token']

    # Extract user_id from access_token for MercadoLibreClient
    user_id = access_token.split('-')[-1] if '-' in access_token else '1'

    async with MercadoLibreClient(access_token, int(user_id)) as client:
        advertiser_id = await client.get_advertiser_id_pads()
        if not advertiser_id:
            logger.error(f"[ERRO] Não foi possível obter advertiser de PADS. Abortando {merchant}.")
            return

        # 1) Listar campanhas
        campaigns = await client.list_advertiser_campaigns(advertiser_id, DATE_FROM_TWO_MONTHS, DATE_TO)

        if not campaigns:
            logger.error(f"[ERRO] Nenhuma campanha encontrada {merchant}.")
            return

        campaigns_list = []
        # Define the output file path
        output_file = 'output_campaigns/output_campaigns.csv'

        for campaign in campaigns:
            if campaign['date_created'].split("-")[0] != '2025':
                campaign['date_created'] = DATE_FROM_TWO_MONTHS
            campaign_data = await client.fetch_campaign_data(campaign['id'], campaign['date_created'].split("T")[0], DATE_TO)
            if not campaign_data:
                logger.error(f"[ERRO] Não foi possível obter dados da campanha {campaign['id']}.")
                continue

            if campaign_data.get('metrics').get("clicks"):
                    campaign_products = await client.list_product_ads_items(advertiser_id, campaign['id'], DATE_FROM, DATE_TO)
                    report_file_path = f'output_campaigns/{merchant}__{campaign['id']}.txt'
                    with open(report_file_path, 'w', encoding='utf-8') as f:
                        f.write(str(generate_whatsapp_performance_report(campaign_data, None, 'pt')))

                    await generate_complete_perfromance_report(campaign_data, campaign_products, campaign['id'], merchant, access_token, 'pt_BR')

        # Normalize and append to main CSV file
        df = pd.json_normalize(campaign_data, sep='_')
        if not df.empty:
            # Add user ID column to identify which user this data belongs to
            df['user_id'] = merchant
            df['merchant_id'] = merchant

            # Check if file exists to determine if we need to write headers
            file_exists = os.path.isfile(output_file)

            # Append to the CSV file with or without headers
            df.to_csv(output_file, mode='a', header=not file_exists, index=False)

            # Also add to campaigns_list for potential future use
            campaigns_list.append(df)
            # else:
            #     if campaign_data['date_created'].split("-")[0] != '2025':
            #         logger.warning(f"[WARN] Campanha {campaign['id']} Antiga")
            #     else:
            #         time_delta = datetime.strptime(campaign_data["date_created"].split('T')[0], "%Y-%m-%d") - datetime.now()
            #         logger.warning(f"[WARN] Campanha {campaign['id']} criada faz {time_delta.days} dias e tem metricas zeradas")
        else:
            logger.warning(f"[WARN] Não foi possível obter dados da campanha {campaign['id']}.")

    return campaigns_list

async def main():
    os.makedirs('output_campaigns', exist_ok=True)

    # Make sure the output file doesn't exist at the start to avoid appending to an old file
    output_file = 'output_campaigns/output_campaigns.csv'
    if os.path.exists(output_file):
        os.remove(output_file)

    # Set up cache directory
    cache_dir = os.path.join(os.getcwd(), 'cache')
    os.makedirs(cache_dir, exist_ok=True)

    # Configure the cache with a 3-hour expiration time (10800 seconds)
    logger.info(f"Setting up cache in directory: {cache_dir} with 3-hour expiration")

    # Create a file cache with 3-hour expiration
    cache = FileBackend(
        cache_name=cache_dir,
        expire_after=3600*3,
        allowed_methods=('GET', 'POST'),  # Cache both GET and POST requests
        include_headers=True,             # Include headers in the cache key
        ignored_params=['access_token'],  # Don't include access tokens in cache keys for security
        cache_control=True                # Respect Cache-Control headers from the server
    )

    # Create retry options for aiohttp-retry
    retry_options = create_retry_options(
        max_retries=5,
        initial_backoff=2,
        max_backoff=120
    )

    # Create a cached session
    cached_session = CachedSession(cache=cache)

    # Wrap the cached session with RetryClient for automatic retries
    async with RetryClient(
        client_session=cached_session,
        retry_options=retry_options
    ) as session:
        logger.info("Using cached session with FileBackend (expires=3600s)")

        with open('merchants.txt', 'r') as f:
            merchants_ids = [(uid.strip()) for uid in f.read().split(',')]

        with open('seller_ids.txt', 'r') as f:
            seller_ids = [r.strip() for r in f.read().split(',')]
            # check if not empty
            if seller_ids[0] == '':
                seller_ids = []

        go_bots_data = await gobots_service.get_api_response(session)
        if not go_bots_data:
            logger.error("Failed to fetch GoBots data")
            return

        # Ensure go_bots_data is a list
        if not isinstance(go_bots_data, list):
            logger.error("GoBots data is not in expected list format")
            return

        # Use the new filtering function
        filters = {
            'merchant_ids': merchants_ids,
            'seller_ids': seller_ids if seller_ids else None
        }

        all_merchants_matches = gobots_service.filter_merchants_data(go_bots_data, filters)

        # Group by merchant_id to process one at a time
        merchants_by_id = {}
        for match in all_merchants_matches:
            merchant_id = match['merchant']
            if merchant_id not in merchants_by_id:
                merchants_by_id[merchant_id] = []
            merchants_by_id[merchant_id].append(match)

        # Process each merchant
        for merchant_id, merchants_matches in merchants_by_id.items():
            tasks = [gerar_relatorio_completo(session, merchant) for merchant in merchants_matches[:1]]
            await asyncio.gather(*tasks)

            # No need to write to CSV at the end since each task already wrote its data
            logger.info(f"[INFO] Report saved to {output_file}")

if __name__ == "__main__":
    asyncio.run(main())
